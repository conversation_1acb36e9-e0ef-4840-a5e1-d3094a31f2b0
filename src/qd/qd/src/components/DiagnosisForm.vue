<template>
  <div class="diagnosis-overlay">
    <div class="diagnosis-modal">
      <div class="modal-header">
        <h3>{{ isViewMode ? '查看诊断处方' : '开具诊断处方' }}</h3>
        <button @click="$emit('close')" class="close-btn">✕</button>
      </div>

      <div class="modal-content">
        <!-- 患者信息 -->
        <div class="patient-info-section">
          <h4>患者信息</h4>
          <div class="patient-details">
            <div class="info-row">
              <span class="label">姓名:</span>
              <span class="value">{{ appointment.profileOwnerName }}</span>
            </div>
            <div class="info-row">
              <span class="label">性别:</span>
              <span class="value">{{ getGenderText(appointment.profileGender) }}</span>
            </div>
            <div class="info-row">
              <span class="label">年龄:</span>
              <span class="value">{{ getAge(appointment.birthDate) }}岁</span>
            </div>
            <div class="info-row">
              <span class="label">电话:</span>
              <span class="value">{{ appointment.phoneNumber || '未提供' }}</span>
            </div>
            <div class="info-row">
              <span class="label">预约时间:</span>
              <span class="value">{{ appointment.appointmentDate }} {{ appointment.appointmentTime }}</span>
            </div>
          </div>
        </div>

        <!-- 诊断信息 -->
        <div class="diagnosis-section">
          <h4>临床诊断</h4>
          <textarea
            v-model="formData.diagnosis"
            :readonly="isViewMode"
            placeholder="请输入临床诊断信息..."
            rows="4"
            class="diagnosis-input"
            :class="{ 'readonly': isViewMode }"
          ></textarea>
        </div>

        <!-- 药品信息 -->
        <div class="medications-section">
          <div class="section-header">
            <h4>处方药品</h4>
            <button 
              v-if="!isViewMode" 
              @click="addMedication" 
              class="add-medication-btn"
            >
              + 添加药品
            </button>
          </div>

          <div v-if="formData.medications.length === 0" class="no-medications">
            暂无药品信息
          </div>

          <div 
            v-for="(medication, index) in formData.medications" 
            :key="index" 
            class="medication-item"
          >
            <div class="medication-header">
              <span class="medication-title">药品 {{ index + 1 }}</span>
              <button 
                v-if="!isViewMode" 
                @click="removeMedication(index)" 
                class="remove-btn"
              >
                删除
              </button>
            </div>
            
            <div class="medication-form">
              <div class="form-row">
                <div class="form-group">
                  <label>药品名称</label>
                  <input
                    v-model="medication.name"
                    :readonly="isViewMode"
                    placeholder="请输入药品名称"
                    class="form-input"
                    :class="{ 'readonly': isViewMode }"
                  />
                </div>
                <div class="form-group">
                  <label>规格</label>
                  <input
                    v-model="medication.specification"
                    :readonly="isViewMode"
                    placeholder="如: 0.25g"
                    class="form-input"
                    :class="{ 'readonly': isViewMode }"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>数量</label>
                  <input
                    v-model.number="medication.quantity"
                    :readonly="isViewMode"
                    type="number"
                    min="1"
                    placeholder="数量"
                    class="form-input"
                    :class="{ 'readonly': isViewMode }"
                  />
                </div>
                <div class="form-group">
                  <label>用药频次</label>
                  <input
                    v-model="medication.frequency"
                    :readonly="isViewMode"
                    placeholder="如: 每日3次"
                    class="form-input"
                    :class="{ 'readonly': isViewMode }"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group full-width">
                  <label>用法用量</label>
                  <input
                    v-model="medication.dosage"
                    :readonly="isViewMode"
                    placeholder="如: 每次1片，饭后服用"
                    class="form-input"
                    :class="{ 'readonly': isViewMode }"
                  />
                </div>
              </div>

              <div class="form-row">
                <div class="form-group full-width">
                  <label>备注</label>
                  <input
                    v-model="medication.notes"
                    :readonly="isViewMode"
                    placeholder="特殊说明（可选）"
                    class="form-input"
                    :class="{ 'readonly': isViewMode }"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button @click="$emit('close')" class="cancel-btn">
          {{ isViewMode ? '关闭' : '取消' }}
        </button>
        <button 
          v-if="!isViewMode" 
          @click="submitDiagnosis" 
          :disabled="submitting || !canSubmit"
          class="submit-btn"
        >
          <span v-if="submitting">提交中...</span>
          <span v-else>开具处方</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { createPrescription, getPrescriptionByAppointment } from '@/api/prescriptions'

const props = defineProps({
  appointment: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'success'])

// 响应式数据
const submitting = ref(false)
const isViewMode = ref(false)
const existingPrescription = ref(null)

const formData = ref({
  diagnosis: '',
  medications: []
})

// 计算属性
const canSubmit = computed(() => {
  return formData.value.diagnosis.trim() && formData.value.medications.length > 0
})

// 方法
const getGenderText = (gender) => {
  const genderMap = {
    'MALE': '男',
    'FEMALE': '女',
    'male': '男',
    'female': '女',
    'OTHER': '其他'
  }
  return genderMap[gender] || '未知'
}

const getAge = (birthDate) => {
  if (!birthDate) return '未知'
  const birth = new Date(birthDate)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  return age
}

const addMedication = () => {
  formData.value.medications.push({
    name: '',
    specification: '',
    quantity: 1,
    frequency: '',
    dosage: '',
    notes: ''
  })
}

const removeMedication = (index) => {
  formData.value.medications.splice(index, 1)
}

const loadExistingPrescription = async () => {
  try {
    const response = await getPrescriptionByAppointment(props.appointment.id)
    if (response.data.code === 200 && response.data.data) {
      existingPrescription.value = response.data.data
      isViewMode.value = true
      
      // 填充表单数据
      formData.value.diagnosis = existingPrescription.value.diagnosis || ''
      formData.value.medications = existingPrescription.value.medications || []
    }
  } catch (error) {
    console.log('暂无诊断记录，进入新建模式')
    isViewMode.value = false
  }
}

const submitDiagnosis = async () => {
  if (!canSubmit.value) return

  submitting.value = true
  try {
    const prescriptionData = {
      profileId: props.appointment.profileId,
      appointmentId: props.appointment.id,
      diagnosis: formData.value.diagnosis.trim(),
      medications: formData.value.medications.filter(med => med.name.trim())
    }

    const response = await createPrescription(prescriptionData)
    if (response.data.code === 200) {
      emit('success', '诊断处方开具成功！')
    } else {
      alert('开具处方失败：' + response.data.message)
    }
  } catch (error) {
    console.error('开具处方失败:', error)
    alert('开具处方失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  // 检查是否已有诊断记录
  if (props.appointment.hasDiagnosis) {
    loadExistingPrescription()
  } else {
    // 新建模式，添加一个默认药品
    addMedication()
  }
})
</script>

<style scoped>
.diagnosis-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.diagnosis-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.patient-info-section,
.diagnosis-section,
.medications-section {
  margin-bottom: 24px;
}

.patient-info-section h4,
.diagnosis-section h4,
.medications-section h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.patient-details {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #6b7280;
  width: 80px;
  flex-shrink: 0;
}

.value {
  color: #1f2937;
}

.diagnosis-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  min-height: 100px;
}

.diagnosis-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.diagnosis-input.readonly {
  background: #f9fafb;
  color: #6b7280;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.add-medication-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.add-medication-btn:hover {
  background: #2563eb;
}

.no-medications {
  text-align: center;
  color: #6b7280;
  padding: 40px;
  background: #f9fafb;
  border-radius: 8px;
}

.medication-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
}

.medication-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.medication-title {
  font-weight: 500;
  color: #1f2937;
}

.remove-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.remove-btn:hover {
  background: #dc2626;
}

.medication-form {
  padding: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  flex: 1;
}

.form-group.full-width {
  flex: none;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.readonly {
  background: #f9fafb;
  color: #6b7280;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.cancel-btn,
.submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.submit-btn {
  background: #10b981;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #059669;
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .diagnosis-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .form-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .modal-content {
    padding: 16px;
  }
}
</style>
