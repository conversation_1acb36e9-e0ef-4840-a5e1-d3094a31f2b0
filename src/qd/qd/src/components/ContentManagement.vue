<template>
  <div class="content-management">
    <div class="page-header">
      <h1>内容管理</h1>
      <button class="publish-btn" @click="showPublishDialog">
        <span class="icon">📝</span>
        发布新内容
      </button>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-tabs">
        <div 
          v-for="tab in filterTabs" 
          :key="tab.value"
          :class="['filter-tab', { active: currentTab === tab.value }]"
          @click="handleTabChange(tab.value)"
        >
          {{ tab.label }}
        </div>
      </div>

      <div class="filter-controls">
        <div class="date-range">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </div>
      </div>
    </div>

    <!-- 内容列表 -->
    <div class="content-list">
      <div class="list-header">
        <div class="col-title">标题</div>
        <div class="col-author">作者</div>
        <div class="col-type">类型</div>
        <div class="col-date">发布时间</div>
        <div class="col-actions">操作</div>
      </div>

      <div v-if="loading" class="loading-state">
        <el-icon class="loading-icon"><loading /></el-icon>
        加载中...
      </div>

      <div v-else-if="contents.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <p>暂无内容</p>
        <button class="publish-btn" @click="showPublishDialog">
          立即发布内容
        </button>
      </div>

      <div v-else class="list-content">
        <div v-for="content in contents" :key="content.id" class="list-item">
          <div class="col-title">{{ content.title }}</div>
          <div class="col-author">{{ content.authorName || '未知作者' }}</div>
          <div class="col-type">
            <span :class="['type-badge', content.contentType]">
              {{ getContentTypeText(content.contentType) }}
            </span>
          </div>
          <div class="col-date">{{ formatDate(content.publishedAt) }}</div>
          <div class="col-actions">
            <button class="action-btn view" @click="viewContent(content)">查看</button>
            <button class="action-btn edit" @click="editContent(content)">编辑</button>
            <button class="action-btn delete" @click="deleteContent(content)">删除</button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { getContents, deleteContent as deleteContentApi } from '@/api/admin'
import { ElMessage, ElDatePicker, ElPagination, ElMessageBox } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

// 路由
const router = useRouter()

// 状态定义
const contents = ref([])
const loading = ref(false)
const currentTab = ref('all')
const dateRange = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 筛选标签定义
const filterTabs = [
  { label: '全部内容', value: 'all' },
  { label: '健康资讯', value: 'news' },
  { label: '社区活动', value: 'activity' },
  { label: '医生指导', value: 'guidance' }
]

// 加载内容列表
const loadContents = async () => {
  loading.value = true
  try {
    const params = {
      type: currentTab.value === 'all' ? null : currentTab.value,
      page: currentPage.value - 1, // 后端分页从0开始
      size: pageSize.value
    }

    if (dateRange.value && dateRange.value.length === 2) {
      params.startDate = dateRange.value[0]
      params.endDate = dateRange.value[1]
    }

    const response = await getContents(params)
    if (response && response.data) {
      contents.value = response.data.content || []
      total.value = response.data.total || 0
    } else {
      contents.value = []
      total.value = 0
    }
  } catch (error) {
    ElMessage.error('加载内容列表失败')
    console.error('Failed to load contents:', error)
    contents.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 事件处理函数
const handleTabChange = (tab) => {
  currentTab.value = tab
  currentPage.value = 1
  loadContents()
}

const handleDateChange = () => {
  currentPage.value = 1
  loadContents()
}

const handlePageChange = (page) => {
  currentPage.value = page
  loadContents()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadContents()
}

const getContentTypeText = (type) => {
  const typeMap = {
    news: '健康资讯',
    activity: '社区活动',
    guidance: '医生指导'
  }
  return typeMap[type] || '未知类型'
}

const formatDate = (date) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 查看内容
const viewContent = (content) => {
  router.push(`/admin/contents/${content.id}`)
}

// 编辑内容
const editContent = (content) => {
  router.push(`/admin/contents/${content.id}/edit`)
}

// 删除内容
const deleteContent = async (content) => {
  try {
    await ElMessageBox.confirm('确定要删除这条内容吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteContentApi(content.id)
    ElMessage.success('删除成功')
    loadContents() // 重新加载列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('Failed to delete content:', error)
    }
  }
}

// 发布新内容
const showPublishDialog = () => {
  router.push('/admin/contents/create')
}

// 初始化
onMounted(() => {
  loadContents()
})
</script>

<style scoped>
.content-management {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.publish-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.publish-btn:hover {
  background-color: #40a9ff;
}

.filter-section {
  margin-bottom: 24px;
}

.filter-tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.filter-tab {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.filter-tab:hover {
  background-color: #f0f0f0;
}

.filter-tab.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.filter-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.content-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  padding: 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
}

.list-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.type-badge.news {
  background-color: #e6f7ff;
  color: #1890ff;
}

.type-badge.activity {
  background-color: #f6ffed;
  color: #52c41a;
}

.type-badge.guidance {
  background-color: #fff7e6;
  color: #fa8c16;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
  font-size: 12px;
  transition: all 0.3s;
}

.action-btn.view {
  background-color: #e6f7ff;
  color: #1890ff;
}

.action-btn.edit {
  background-color: #f6ffed;
  color: #52c41a;
}

.action-btn.delete {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.action-btn:hover {
  opacity: 0.8;
}

.loading-state {
  padding: 48px;
  text-align: center;
  color: #8c8c8c;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-icon {
  font-size: 24px;
  animation: rotate 1s linear infinite;
}

.empty-state {
  padding: 48px;
  text-align: center;
  color: #8c8c8c;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.pagination-section {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 日期选择器样式 */
:deep(.el-date-editor) {
  width: 320px;
}

:deep(.el-date-editor .el-range-separator) {
  padding: 0 8px;
}

:deep(.el-date-editor .el-range__icon) {
  margin: 0 4px;
}
</style> 