<template>
  <div class="appointment-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="header-info">
        <h3>👥 预约患者列表</h3>
        <span class="appointment-count">共 {{ totalElements }} 个预约</span>
      </div>
      <div class="header-actions">
        <button @click="$emit('refresh')" class="refresh-btn" :disabled="loading">
          <span class="refresh-icon" :class="{ 'spinning': loading }">🔄</span>
          刷新
        </button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-group">
        <label>状态筛选:</label>
        <select v-model="statusFilter" @change="applyFilters" class="filter-select">
          <option v-for="option in statusOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label>日期筛选:</label>
        <select v-model="dateFilter" @change="applyFilters" class="filter-select">
          <option v-for="option in dateOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>
      <div class="filter-group">
        <label>搜索:</label>
        <input
          v-model="searchKeyword"
          @input="applyFilters"
          placeholder="搜索患者姓名、手机号..."
          class="search-input"
        />
      </div>
      <div class="filter-group">
        <label>排序:</label>
        <select v-model="sortBy" @change="applySorting" class="filter-select">
          <option value="date">按日期</option>
          <option value="time">按时间</option>
          <option value="status">按状态</option>
          <option value="priority">按优先级</option>
        </select>
        <button @click="toggleSortOrder" class="sort-order-btn">
          {{ sortOrder === 'asc' ? '↑' : '↓' }}
        </button>
      </div>
    </div>

    <!-- 预约列表 -->
    <div class="appointment-table">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">⏳</div>
        <p>加载中...</p>
      </div>

      <div v-else-if="displayAppointments.length === 0" class="empty-state">
        <div class="empty-icon">📅</div>
        <h4>暂无预约数据</h4>
        <p>{{ getEmptyMessage() }}</p>
      </div>

      <div v-else class="table-container">
        <!-- 表格头部 -->
        <div class="table-header">
          <div class="col-patient">患者信息</div>
          <div class="col-time">预约时间</div>
          <div class="col-reason">预约原因</div>
          <div class="col-status">状态</div>
          <div class="col-actions">操作</div>
        </div>

        <!-- 表格内容 -->
        <div
          v-for="appointment in displayAppointments"
          :key="appointment.id"
          class="table-row"
          :class="getRowClass(appointment)"
        >
          <!-- 患者信息 -->
          <div class="col-patient">
            <div class="patient-info">
              <div class="patient-avatar">
                <span class="avatar-text">
                  {{ getPatientInitial(constructPatientObject(appointment)) }}
                </span>
              </div>
              <div class="patient-details">
                <div class="patient-name">{{ getPatientName(constructPatientObject(appointment)) }}</div>
                <div class="patient-meta">
                  {{ getPatientAge(appointment.birthDate) }}岁
                  {{ getPatientGender(appointment.profileGender) }}
                </div>
                <div class="patient-phone">{{ appointment.phoneNumber || '未提供' }}</div>
              </div>
            </div>
          </div>

          <!-- 预约时间 -->
          <div class="col-time">
            <div class="time-info">
              <div class="appointment-date">{{ formatAppointmentDate(appointment.appointmentDate) }}</div>
              <div class="appointment-time">{{ formatTimeDisplay(appointment.appointmentTime) }}</div>
              <div class="relative-time">{{ getRelativeTime(appointment.appointmentDate) }}</div>
            </div>
          </div>

          <!-- 预约原因 -->
          <div class="col-reason">
            <div class="reason-text">
              {{ appointment.reason || '常规检查' }}
            </div>
          </div>

          <!-- 状态 -->
          <div class="col-status">
            <span class="status-badge" :class="getAppointmentStatusClass(appointment.status)">
              {{ getAppointmentStatusText(appointment.status) }}
            </span>
            <div class="diagnosis-status" :class="getDiagnosisStatusClass(appointment)">
              {{ getDiagnosisStatusText(appointment) }}
            </div>
            <div class="priority-indicator" :class="getPriorityClass(getAppointmentPriority(appointment))">
              {{ getPriorityText(getAppointmentPriority(appointment)) }}
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="col-actions">
            <div class="action-buttons">
              <!-- 查看详情 -->
              <button
                @click="$emit('view', appointment)"
                class="action-btn view-btn"
                title="查看详情"
              >
                👁️
              </button>

              <!-- 确认预约 -->
              <button
                v-if="canOperateAppointment(appointment, 'confirm')"
                @click="$emit('confirm', appointment)"
                class="action-btn confirm-btn"
                title="确认预约"
              >
                ✅
              </button>

              <!-- 完成诊疗 -->
              <button
                v-if="canOperateAppointment(appointment, 'complete')"
                @click="$emit('complete', appointment)"
                class="action-btn complete-btn"
                title="完成诊疗"
              >
                ✔️
              </button>

              <!-- 诊断处方 -->
              <button
                v-if="canDiagnoseAppointment(appointment)"
                @click="$emit('diagnose', appointment)"
                class="action-btn diagnose-btn"
                :title="appointment.hasDiagnosis ? '查看诊断' : '开具诊断'"
              >
                {{ appointment.hasDiagnosis ? '📋' : '🩺' }}
              </button>

              <!-- 添加记录 -->
              <button
                v-if="canOperateAppointment(appointment, 'addRecord')"
                @click="$emit('addRecord', appointment)"
                class="action-btn record-btn"
                title="添加诊疗记录"
              >
                📝
              </button>

              <!-- 查看病历 -->
              <button
                @click="$emit('viewHistory', constructPatientObject(appointment))"
                class="action-btn history-btn"
                title="查看病历"
              >
                📋
              </button>

              <!-- 取消预约 -->
              <button
                v-if="canOperateAppointment(appointment, 'cancel')"
                @click="$emit('cancel', appointment)"
                class="action-btn cancel-btn"
                title="取消预约"
              >
                ❌
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页器 -->
    <div v-if="totalPages > 1" class="pagination">
      <button
        @click="goToPage(currentPage - 1)"
        :disabled="currentPage === 0"
        class="page-btn"
      >
        上一页
      </button>
      
      <div class="page-numbers">
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="goToPage(page - 1)"
          :class="{ active: page - 1 === currentPage }"
          class="page-number"
        >
          {{ page }}
        </button>
      </div>
      
      <button
        @click="goToPage(currentPage + 1)"
        :disabled="currentPage >= totalPages - 1"
        class="page-btn"
      >
        下一页
      </button>
      
      <div class="page-info">
        第 {{ currentPage + 1 }} 页，共 {{ totalPages }} 页
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  getAppointmentStatusText,
  getAppointmentStatusClass,
  getAppointmentPriority,
  getPriorityClass,
  canOperateAppointment,
  getPatientAge,
  formatAppointmentDate,
  getAppointmentFilterOptions,
  getDateFilterOptions,
  sortAppointments,
  calculateAppointmentStats
} from '@/utils/appointmentUtils'
import {
  formatTimeDisplay,
  getRelativeDateDescription,
  isToday,
  isTomorrow,
  isPastDate
} from '@/utils/dateUtils'

// Props & Emits
const props = defineProps({
  appointments: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  totalElements: {
    type: Number,
    default: 0
  },
  totalPages: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 0
  },
  pageSize: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits([
  'refresh', 'view', 'confirm', 'complete', 'addRecord',
  'viewHistory', 'cancel', 'pageChange', 'filterChange', 'diagnose'
])

// 响应式数据
const statusFilter = ref('all')
const dateFilter = ref('all')
const searchKeyword = ref('')
const sortBy = ref('date')
const sortOrder = ref('asc')

// 筛选选项
const statusOptions = getAppointmentFilterOptions()
const dateOptions = getDateFilterOptions()

// 计算属性
const displayAppointments = computed(() => {
  let filtered = [...props.appointments]
  
  // 应用排序
  filtered = sortAppointments(filtered, sortBy.value, sortOrder.value)
  
  return filtered
})

const visiblePages = computed(() => {
  const total = props.totalPages
  const current = props.currentPage + 1
  const pages = []
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }
  
  return pages
})

// 方法
const getPatientName = (patient) => {
  return patient?.name || patient?.profileOwnerName || '未知患者'
}

/**
 * 从预约对象构造患者对象
 * 解决后端AppointmentDTO中患者信息不是嵌套对象的问题
 */
const constructPatientObject = (appointment) => {
  if (!appointment) return null

  // 从预约对象中提取患者信息，构造patient对象
  return {
    id: appointment.profileId,           // 使用健康档案ID作为患者ID
    userId: appointment.userId,          // 用户ID
    profileId: appointment.profileId,    // 健康档案ID
    name: appointment.profileOwnerName,  // 患者姓名
    profileOwnerName: appointment.profileOwnerName,
    gender: appointment.profileGender,   // 患者性别
    phoneNumber: appointment.phoneNumber, // 电话号码（如果有）
    birthDate: appointment.birthDate     // 出生日期（如果有）
  }
}

const getPatientInitial = (patient) => {
  const name = getPatientName(patient)
  return name.charAt(0).toUpperCase()
}

const getPatientGender = (gender) => {
  const genderMap = {
    'male': '男',
    'female': '女',
    'MALE': '男',
    'FEMALE': '女',
    'OTHER': '其他'
  }
  return genderMap[gender] || ''
}

const getRelativeTime = (date) => {
  return getRelativeDateDescription(date)
}

const getPriorityText = (priority) => {
  const priorityMap = {
    'urgent': '紧急',
    'high': '高',
    'normal': '普通',
    'low': '低'
  }
  return priorityMap[priority] || '普通'
}

const getRowClass = (appointment) => {
  const classes = []
  
  if (isToday(appointment.appointmentDate)) {
    classes.push('today')
  } else if (isTomorrow(appointment.appointmentDate)) {
    classes.push('tomorrow')
  } else if (isPastDate(appointment.appointmentDate)) {
    classes.push('past')
  }
  
  classes.push(`status-${appointment.status}`)
  classes.push(`priority-${getAppointmentPriority(appointment)}`)
  
  return classes
}

const getEmptyMessage = () => {
  if (statusFilter.value !== 'all' || dateFilter.value !== 'all' || searchKeyword.value.trim()) {
    return '没有找到符合条件的预约，请调整筛选条件'
  }
  return '暂无预约数据'
}

const applyFilters = () => {
  const filters = {
    status: statusFilter.value,
    date: dateFilter.value,
    keyword: searchKeyword.value.trim()
  }
  emit('filterChange', filters)
}

const applySorting = () => {
  // 排序在计算属性中处理
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
}

const goToPage = (page) => {
  if (page >= 0 && page < props.totalPages) {
    emit('pageChange', page)
  }
}

// 诊断相关方法
const canDiagnoseAppointment = (appointment) => {
  // 医生可以对任何预约进行诊断，无时间限制
  return true
}

const getDiagnosisStatusText = (appointment) => {
  if (appointment.hasDiagnosis) {
    return '已诊断'
  }
  return '未诊断'
}

const getDiagnosisStatusClass = (appointment) => {
  if (appointment.hasDiagnosis) {
    return 'diagnosis-completed'
  }
  return 'diagnosis-pending'
}

// 监听筛选条件变化
watch([statusFilter, dateFilter, searchKeyword], () => {
  applyFilters()
}, { deep: true })
</script>

<style scoped>
.appointment-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.header-info h3 {
  margin: 0 0 4px 0;
  color: #1e40af;
  font-size: 18px;
  font-weight: 600;
}

.appointment-count {
  color: #6b7280;
  font-size: 14px;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background: #e5e7eb;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.filters {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

.search-input {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  width: 200px;
}

.sort-order-btn {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.sort-order-btn:hover {
  background: #f3f4f6;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 18px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

.table-container {
  overflow-x: auto;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1.5fr;
  gap: 16px;
  padding: 16px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1.5fr 1fr 1.5fr;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f8fafc;
}

.table-row.today {
  background: #fef3c7;
  border-left: 4px solid #f59e0b;
}

.table-row.tomorrow {
  background: #dbeafe;
  border-left: 4px solid #3b82f6;
}

.table-row.past {
  opacity: 0.6;
  background: #f9fafb;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.patient-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  flex-shrink: 0;
}

.patient-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.patient-meta {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.patient-phone {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.appointment-date {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.appointment-time {
  font-size: 13px;
  color: #3b82f6;
  font-weight: 500;
}

.relative-time {
  font-size: 12px;
  color: #6b7280;
}

.reason-text {
  font-size: 14px;
  color: #374151;
  line-height: 1.4;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  display: inline-block;
  margin-bottom: 4px;
}

.status-booked {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-completed {
  background: #dcfce7;
  color: #166534;
}

.status-cancelled {
  background: #fee2e2;
  color: #991b1b;
}

.priority-indicator {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 8px;
  text-align: center;
}

.priority-urgent {
  background: #fee2e2;
  color: #991b1b;
}

.priority-high {
  background: #fef3c7;
  color: #92400e;
}

.priority-normal {
  background: #f3f4f6;
  color: #6b7280;
}

.priority-low {
  background: #f0fdf4;
  color: #166534;
}

.diagnosis-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-top: 4px;
  text-align: center;
  font-weight: 500;
}

.diagnosis-completed {
  background: #dcfce7;
  color: #166534;
}

.diagnosis-pending {
  background: #fef3c7;
  color: #92400e;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.action-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.view-btn {
  background: #f0f9ff;
  color: #0369a1;
}

.view-btn:hover {
  background: #0369a1;
  color: white;
}

.confirm-btn {
  background: #f0fdf4;
  color: #166534;
}

.confirm-btn:hover {
  background: #22c55e;
  color: white;
}

.complete-btn {
  background: #dbeafe;
  color: #1d4ed8;
}

.complete-btn:hover {
  background: #3b82f6;
  color: white;
}

.diagnose-btn {
  background: #f0f9ff;
  color: #0369a1;
}

.diagnose-btn:hover {
  background: #0369a1;
  color: white;
}

.record-btn {
  background: #fef3c7;
  color: #92400e;
}

.record-btn:hover {
  background: #f59e0b;
  color: white;
}

.history-btn {
  background: #f3f4f6;
  color: #374151;
}

.history-btn:hover {
  background: #6b7280;
  color: white;
}

.cancel-btn {
  background: #fee2e2;
  color: #dc2626;
}

.cancel-btn:hover {
  background: #ef4444;
  color: white;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
  background: #f3f4f6;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-number:hover {
  background: #f3f4f6;
}

.page-number.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.page-info {
  margin-left: 16px;
  font-size: 14px;
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .search-input {
    width: 100%;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
  }
  
  .pagination {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .page-info {
    margin-left: 0;
    margin-top: 8px;
    width: 100%;
    text-align: center;
  }
}
</style>
