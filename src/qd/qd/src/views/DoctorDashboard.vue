<template>
  <div class="doctor-layout">
    <!-- 左侧导航栏 -->
    <aside class="doctor-sidebar">
      <!-- 医生头像和信息 -->
      <div class="doctor-profile-section">
        <div class="doctor-info">
          <div class="doctor-avatar">{{ userInfo?.nickname?.charAt(0) || 'D' }}</div>
          <div class="doctor-details">
            <div class="doctor-name">{{ userInfo?.nickname || '医生' }}</div>
            <div class="doctor-dept">{{ userInfo?.departmentName || '全科' }}</div>
            <div class="doctor-title">{{ userInfo?.title || '主治医师' }}</div>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="nav-divider"></div>

      <nav class="sidebar-nav">
        <!-- 工作台 -->
        <div class="nav-item active" @click="setActiveTab('workbench')">
          <div class="nav-icon">🏠</div>
          <span>工作台</span>
        </div>

        <!-- 在线问诊 -->
        <div class="nav-item" @click="goToConsultations">
          <div class="nav-icon">💬</div>
          <span>在线问诊</span>
          <div class="task-badge" v-if="pendingConsultations > 0">{{ pendingConsultations }}</div>
        </div>

        <!-- 电子处方 -->
        <div class="nav-item" @click="goToPrescriptions">
          <div class="nav-icon">📋</div>
          <span>电子处方</span>
        </div>

        <!-- 预约管理 -->
        <div class="nav-item" @click="goToAppointments">
          <div class="nav-icon">📋</div>
          <span>预约管理</span>
        </div>

        <!-- 排班管理 -->
        <div class="nav-item" @click="goToSchedule">
          <div class="nav-icon">📅</div>
          <span>排班管理</span>
        </div>

        <!-- 健康指导 -->
        <div class="nav-item" @click="goToGuidance">
          <div class="nav-icon">📝</div>
          <span>健康指导</span>
        </div>

        <!-- 我的患者 -->
        <div class="nav-item" @click="setActiveTab('patients')">
          <div class="nav-icon">👥</div>
          <span>我的患者</span>
        </div>

        <!-- 统计分析 -->
        <div class="nav-item" @click="goToStatistics">
          <div class="nav-icon">📊</div>
          <span>统计分析</span>
        </div>

        <!-- 个人信息 -->
        <div class="nav-item" @click="goToProfile">
          <div class="nav-icon">⚙️</div>
          <span>个人信息</span>
        </div>
      </nav>
      
      <div class="sidebar-footer">
        <button @click="handleLogout" class="logout-btn">
          <div class="logout-icon">🚪</div>
          退出
        </button>
      </div>
    </aside>

    <!-- 主内容区 -->
    <main class="doctor-main">
      <!-- 工作台 -->
      <div v-if="activeTab === 'workbench'" class="workbench-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>工作台</h1>
          <div class="current-time">{{ getCurrentDateTime() }}</div>
        </div>

        <!-- 数据概览卡片 -->
        <div class="summary-cards">
          <div class="summary-card primary">
            <div class="summary-icon">👥</div>
            <div class="summary-content">
              <div class="summary-number">{{ todayPatients }}</div>
              <div class="summary-label">今日待就诊患者</div>
            </div>
          </div>
          <div class="summary-card warning">
            <div class="summary-icon">💬</div>
            <div class="summary-content">
              <div class="summary-number">{{ pendingConsultations }}</div>
              <div class="summary-label">待回复问诊</div>
            </div>
          </div>
          <div class="summary-card success">
            <div class="summary-icon">✅</div>
            <div class="summary-content">
              <div class="summary-number">{{ completedToday }}</div>
              <div class="summary-label">今日已完成</div>
            </div>
          </div>
          <div class="summary-card info">
            <div class="summary-icon">📊</div>
            <div class="summary-content">
              <div class="summary-number">{{ totalPatients }}</div>
              <div class="summary-label">总患者数</div>
            </div>
          </div>
        </div>

        <!-- 主要工作区域 -->
        <div class="work-area">
          <!-- 待处理任务列表 -->
          <div class="task-section">
            <div class="section-header">
              <h2>⚡ 待处理任务</h2>
              <span class="task-count">{{ pendingTasks.length }} 项待办</span>
            </div>
            <div class="task-list">
              <div v-for="task in pendingTasks" :key="task.id" class="task-item" :class="task.priority">
                <div class="task-info">
                  <div class="task-type">{{ task.type }}</div>
                  <div class="task-title">{{ task.title }}</div>
                  <div class="task-patient">患者：{{ task.patientName }}</div>
                  <div class="task-time">{{ task.time }}</div>
                </div>
                <div class="task-actions">
                  <button class="task-btn primary" @click="handleTask(task)">
                    {{ task.actionText }}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 今日日程安排 -->
          <div class="schedule-section">
            <div class="section-header">
              <h2>📅 今日日程</h2>
              <button class="schedule-btn">管理排班</button>
            </div>
            <div class="schedule-table">
              <div class="schedule-header">
                <div class="time-col">时间</div>
                <div class="patient-col">患者</div>
                <div class="status-col">状态</div>
                <div class="action-col">操作</div>
              </div>
              <div v-for="appointment in todaySchedule" :key="appointment.id" class="schedule-row">
                <div class="time-col">{{ appointment.time }}</div>
                <div class="patient-col">
                  <div class="patient-name">{{ appointment.patientName }}</div>
                  <div class="patient-age">{{ appointment.age }}岁 {{ appointment.gender }}</div>
                </div>
                <div class="status-col">
                  <span class="status-badge" :class="appointment.status">
                    {{ getStatusText(appointment.status) }}
                  </span>
                </div>
                <div class="action-col">
                  <button class="action-btn" @click="viewPatientProfile(appointment.patientId)">
                    查看档案
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 其他页面内容占位 -->
      <div v-else class="page-content">
        <div class="coming-soon">
          <div class="coming-soon-icon">🚧</div>
          <h2>{{ getPageTitle() }}</h2>
          <p>此功能正在开发中，敬请期待...</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const activeTab = ref('workbench')

// 模拟数据
const todayPatients = ref(5)
const pendingConsultations = ref(3)
const completedToday = ref(8)
const totalPatients = ref(156)

const pendingTasks = ref([
  {
    id: 1,
    type: '在线问诊',
    title: '咨询高血压用药问题',
    patientName: '张三',
    time: '2分钟前',
    priority: 'urgent',
    actionText: '立即回复'
  },
  {
    id: 2,
    type: '预约就诊',
    title: '复查血糖指标',
    patientName: '李四',
    time: '10:30',
    priority: 'normal',
    actionText: '查看详情'
  },
  {
    id: 3,
    type: '在线问诊',
    title: '询问药物副作用',
    patientName: '王五',
    time: '15分钟前',
    priority: 'normal',
    actionText: '立即回复'
  }
])

const todaySchedule = ref([
  {
    id: 1,
    time: '09:00',
    patientName: '陈小明',
    age: 45,
    gender: '男',
    status: 'waiting',
    patientId: 'p001'
  },
  {
    id: 2,
    time: '09:30',
    patientName: '刘小红',
    age: 38,
    gender: '女',
    status: 'completed',
    patientId: 'p002'
  },
  {
    id: 3,
    time: '10:00',
    patientName: '张大伟',
    age: 52,
    gender: '男',
    status: 'in-progress',
    patientId: 'p003'
  },
  {
    id: 4,
    time: '10:30',
    patientName: '李小花',
    age: 29,
    gender: '女',
    status: 'scheduled',
    patientId: 'p004'
  }
])

const userInfo = computed(() => userStore.userInfo)

// 方法
const setActiveTab = (tab) => {
  activeTab.value = tab
}

const getCurrentDateTime = () => {
  const now = new Date()
  const options = { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    weekday: 'long',
    hour: '2-digit',
    minute: '2-digit'
  }
  return now.toLocaleDateString('zh-CN', options)
}

const getPageTitle = () => {
  const titles = {
    patients: '我的患者',
    consultations: '在线问诊',
    schedule: '我的排班',
    guidance: '健康指导'
  }
  return titles[activeTab.value] || '页面'
}

const getStatusText = (status) => {
  const statusMap = {
    waiting: '等待中',
    'in-progress': '进行中',
    completed: '已完成',
    scheduled: '已预约'
  }
  return statusMap[status] || status
}

const handleTask = (task) => {
  console.log('处理任务:', task)
  // 这里将来连接API
}

const viewPatientProfile = (patientId) => {
  console.log('查看患者档案:', patientId)
  // 这里将来连接API
}

const goToProfile = () => {
  router.push('/doctor/profile')
}

const goToSchedule = () => {
  router.push('/doctor/schedule')
}

const goToAppointments = () => {
  router.push('/doctor/appointments')
}

const goToStatistics = () => {
  router.push('/doctor/statistics')
}

const goToConsultations = () => {
  router.push('/doctor/consultations')
}

const goToPrescriptions = () => {
  router.push('/doctor/prescriptions')
}

const goToGuidance = () => {
  router.push('/doctor/guidance')
}

const handleLogout = async () => {
  await userStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.doctor-layout {
  display: flex;
  min-height: 100vh;
  background: #f1f5f9;
}

/* 医生端侧边栏 - 专业深色主题 */
.doctor-sidebar {
  width: 300px;
  background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 100;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

/* 医生头像和信息区域 */
.doctor-profile-section {
  padding: 24px 20px;
}

.doctor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.doctor-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
}

.doctor-details {
  flex: 1;
}

.doctor-name {
  font-weight: 600;
  color: white;
  font-size: 15px;
  margin-bottom: 2px;
}

.doctor-dept {
  color: #94a3b8;
  font-size: 13px;
  margin-bottom: 2px;
}

.doctor-title {
  color: #64748b;
  font-size: 12px;
}

/* 分隔线 */
.nav-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 20px;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 20px;
  margin: 2px 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-icon {
  font-size: 20px;
  width: 24px;
  text-align: center;
}

.nav-item span {
  font-weight: 500;
  font-size: 15px;
}

.task-badge {
  background: #ef4444;
  color: white;
  font-size: 11px;
  font-weight: 700;
  padding: 3px 7px;
  border-radius: 12px;
  margin-left: auto;
  min-width: 20px;
  text-align: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}



.logout-btn {
  width: 100%;
  padding: 12px 16px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  color: #fca5a5;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
  color: #f87171;
}

.logout-icon {
  font-size: 16px;
}

/* 主内容区 */
.doctor-main {
  flex: 1;
  margin-left: 300px;
  padding: 24px;
  min-height: 100vh;
  background: #f1f5f9;
}

.workbench-content {
  max-width: 1400px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.page-header h1 {
  color: #1e293b;
  font-size: 32px;
  font-weight: 700;
  margin: 0;
}

.current-time {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

/* 数据概览卡片 */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.summary-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  transition: all 0.2s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.summary-card.primary {
  border-left-color: #3b82f6;
}

.summary-card.warning {
  border-left-color: #f59e0b;
}

.summary-card.success {
  border-left-color: #10b981;
}

.summary-card.info {
  border-left-color: #8b5cf6;
}

.summary-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #f8fafc;
}

.summary-content {
  flex: 1;
}

.summary-number {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.summary-label {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

/* 工作区域 */
.work-area {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.task-section,
.schedule-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
}

.section-header h2 {
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.task-count {
  background: #3b82f6;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.schedule-btn {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.schedule-btn:hover {
  background: #e2e8f0;
}

/* 任务列表 */
.task-list {
  padding: 16px 24px 24px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 10px;
  border-left: 4px solid #e2e8f0;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.task-item:hover {
  background: #f1f5f9;
  transform: translateX(4px);
}

.task-item.urgent {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.task-item.urgent:hover {
  background: #fee2e2;
}

.task-item.normal {
  border-left-color: #3b82f6;
}

.task-info {
  flex: 1;
}

.task-type {
  color: #3b82f6;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
}

.task-title {
  color: #1e293b;
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 4px;
}

.task-patient {
  color: #64748b;
  font-size: 13px;
  margin-bottom: 2px;
}

.task-time {
  color: #94a3b8;
  font-size: 12px;
}

.task-actions {
  margin-left: 16px;
}

.task-btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.task-btn.primary {
  background: #3b82f6;
  color: white;
}

.task-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* 日程表格 */
.schedule-table {
  padding: 16px 24px 24px;
}

.schedule-header {
  display: grid;
  grid-template-columns: 80px 1fr 100px 100px;
  gap: 16px;
  padding: 12px 16px;
  background: #f1f5f9;
  border-radius: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 13px;
  color: #475569;
}

.schedule-row {
  display: grid;
  grid-template-columns: 80px 1fr 100px 100px;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  align-items: center;
  transition: all 0.2s ease;
}

.schedule-row:hover {
  background: #f8fafc;
}

.schedule-row:last-child {
  border-bottom: none;
}

.time-col {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

.patient-col {
  display: flex;
  flex-direction: column;
}

.patient-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
  margin-bottom: 2px;
}

.patient-age {
  color: #64748b;
  font-size: 12px;
}

.status-col {
  text-align: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.status-badge.waiting {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.in-progress {
  background: #dbeafe;
  color: #2563eb;
}

.status-badge.completed {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.scheduled {
  background: #f3e8ff;
  color: #7c3aed;
}

.action-col {
  text-align: center;
}

.action-btn {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

/* 其他页面内容 */
.page-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.coming-soon {
  text-align: center;
  padding: 40px;
}

.coming-soon-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.coming-soon h2 {
  color: #1e293b;
  font-size: 24px;
  margin-bottom: 12px;
}

.coming-soon p {
  color: #64748b;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .work-area {
    grid-template-columns: 1fr;
  }

  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .doctor-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .doctor-main {
    margin-left: 0;
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .summary-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .work-area {
    gap: 16px;
  }

  .schedule-header,
  .schedule-row {
    grid-template-columns: 60px 1fr 80px 80px;
    gap: 8px;
    padding: 12px;
  }

  .task-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .task-actions {
    margin-left: 0;
    width: 100%;
  }

  .task-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .doctor-main {
    padding: 12px;
  }

  .schedule-header,
  .schedule-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .schedule-header {
    display: none;
  }

  .schedule-row {
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 8px;
    border: none;
  }
}
</style>
