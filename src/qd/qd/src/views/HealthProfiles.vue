<template>
  <div class="health-profiles">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>健康档案管理</h1>
      <p class="page-description">管理您和家人的健康档案信息</p>
      <div class="header-actions">
        <button @click="showCreateModal = true" class="create-btn">
          <span class="btn-icon">➕</span>
          创建档案
        </button>
        <button @click="refreshProfiles" class="refresh-btn" :disabled="loading">
          <span class="btn-icon">🔄</span>
          刷新
        </button>
      </div>
    </div>

    <!-- 档案列表 -->
    <div class="profiles-container">
      <div v-if="loading" class="loading">
        <div class="loading-spinner"></div>
        <p>正在加载健康档案...</p>
      </div>
      
      <div v-else-if="profiles.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <h3>暂无健康档案</h3>
        <p>请先创建健康档案，这是预约挂号的必要条件</p>
        <button @click="showCreateModal = true" class="create-btn">
          立即创建
        </button>
      </div>
      
      <div v-else class="profiles-grid">
        <div 
          v-for="profile in profiles" 
          :key="profile.id"
          class="profile-card"
        >
          <div class="profile-header">
            <div class="profile-avatar">
              <img v-if="profile.avatarUrl" :src="profile.avatarUrl" :alt="profile.profileOwnerName">
              <div v-else class="avatar-placeholder">
                {{ getInitials(profile.profileOwnerName) }}
              </div>
            </div>
            <div class="profile-info">
              <h3>{{ profile.profileOwnerName }}</h3>
              <p class="profile-details">
                <span v-if="profile.gender">{{ getGenderText(profile.gender) }}</span>
                <span v-if="profile.age">{{ profile.age }}岁</span>
              </p>
            </div>
          </div>
          
          <div class="profile-body">
            <div class="info-item" v-if="profile.birthDate">
              <label>出生日期:</label>
              <span>{{ formatDate(profile.birthDate) }}</span>
            </div>
            <div class="info-item" v-if="profile.idCard">
              <label>身份证号:</label>
              <span>{{ maskIdCard(profile.idCard) }}</span>
            </div>
            <div class="info-item" v-if="profile.medicalHistory">
              <label>病史:</label>
              <span>{{ profile.medicalHistory }}</span>
            </div>
          </div>
          
          <div class="profile-actions">
            <button @click="viewProfile(profile)" class="action-btn view">
              查看详情
            </button>
            <button @click="editProfile(profile)" class="action-btn edit">
              编辑
            </button>
            <button @click="deleteProfile(profile)" class="action-btn delete">
              删除
            </button>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination" v-if="totalPages > 1">
        <button 
          @click="changePage(currentPage - 1)" 
          :disabled="currentPage <= 1"
          class="page-btn"
        >
          上一页
        </button>
        
        <div class="page-numbers">
          <button 
            v-for="page in getPageNumbers()" 
            :key="page"
            @click="changePage(page)"
            :class="['page-number', { active: page === currentPage }]"
          >
            {{ page }}
          </button>
        </div>
        
        <button 
          @click="changePage(currentPage + 1)" 
          :disabled="currentPage >= totalPages"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 创建/编辑档案模态框 -->
    <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModals">
      <div class="modal-content profile-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ isEditMode ? '编辑健康档案' : '创建健康档案' }}</h3>
          <button @click="closeModals" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitProfile" class="profile-form">
            <div class="form-group">
              <label for="profileOwnerName">姓名 *</label>
              <input
                id="profileOwnerName"
                v-model="profileForm.profileOwnerName"
                type="text"
                placeholder="请输入姓名"
                required
              />
            </div>

            <div class="form-group">
              <label for="gender">性别</label>
              <select id="gender" v-model="profileForm.gender">
                <option value="">请选择性别</option>
                <option value="MALE">男</option>
                <option value="FEMALE">女</option>
                <option value="OTHER">其他</option>
              </select>
            </div>

            <div class="form-group">
              <label for="birthDate">出生日期</label>
              <input
                id="birthDate"
                v-model="profileForm.birthDate"
                type="date"
              />
            </div>

            <div class="form-group">
              <label for="idCard">身份证号</label>
              <input
                id="idCard"
                v-model="profileForm.idCard"
                type="text"
                placeholder="请输入身份证号"
                maxlength="18"
              />
            </div>

            <div class="form-group">
              <label for="medicalHistory">病史信息</label>
              <textarea
                id="medicalHistory"
                v-model="profileForm.medicalHistory"
                placeholder="请输入病史信息（可选）"
                rows="3"
              ></textarea>
            </div>

            <div class="form-actions">
              <button type="button" @click="closeModals" class="cancel-btn">
                取消
              </button>
              <button type="submit" class="submit-btn" :disabled="submitting">
                {{ submitting ? '保存中...' : (isEditMode ? '更新' : '创建') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 档案详情模态框 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeModals">
      <div class="modal-content detail-modal" @click.stop>
        <div class="modal-header">
          <h3>健康档案详情</h3>
          <button @click="closeModals" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div v-if="selectedProfile" class="profile-detail">
            <div class="detail-header">
              <div class="detail-avatar">
                <img v-if="selectedProfile.avatarUrl" :src="selectedProfile.avatarUrl" :alt="selectedProfile.profileOwnerName">
                <div v-else class="avatar-placeholder">
                  {{ getInitials(selectedProfile.profileOwnerName) }}
                </div>
              </div>
              <div class="detail-info">
                <h2>{{ selectedProfile.profileOwnerName }}</h2>
                <p v-if="selectedProfile.gender || selectedProfile.age">
                  {{ getGenderText(selectedProfile.gender) }} | {{ selectedProfile.age }}岁
                </p>
              </div>
            </div>
            
            <div class="detail-content">
              <div class="detail-item" v-if="selectedProfile.birthDate">
                <label>出生日期:</label>
                <span>{{ formatDate(selectedProfile.birthDate) }}</span>
              </div>
              <div class="detail-item" v-if="selectedProfile.idCard">
                <label>身份证号:</label>
                <span>{{ selectedProfile.idCard }}</span>
              </div>
              <div class="detail-item" v-if="selectedProfile.medicalHistory">
                <label>病史信息:</label>
                <span>{{ selectedProfile.medicalHistory }}</span>
              </div>
              <div class="detail-item" v-if="selectedProfile.createdAt">
                <label>创建时间:</label>
                <span>{{ formatDateTime(selectedProfile.createdAt) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import * as healthApi from '@/api/health'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const profiles = ref([])
const currentPage = ref(1)
const totalPages = ref(1)
const pageSize = 10

const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDetailModal = ref(false)
const selectedProfile = ref(null)

const profileForm = reactive({
  profileOwnerName: '',
  gender: '',
  birthDate: '',
  idCard: '',
  medicalHistory: ''
})

// 计算属性
const isEditMode = computed(() => showEditModal.value)

const getPageNumbers = () => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
}

// 方法
const loadProfiles = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      size: pageSize
    }

    const response = await healthApi.getHealthProfiles(params)
    
    if (response.data.code === 200) {
      const data = response.data.data
      profiles.value = data.profiles || []
      totalPages.value = data.totalPages || 1
    } else {
      console.error('获取健康档案失败:', response.data.message)
      alert(response.data.message || '获取健康档案失败')
    }
  } catch (error) {
    console.error('获取健康档案失败:', error)
    alert('获取健康档案失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const refreshProfiles = () => {
  loadProfiles()
}

const changePage = (page) => {
  currentPage.value = page
  loadProfiles()
}

const viewProfile = async (profile) => {
  try {
    const response = await healthApi.getHealthProfile(profile.id)
    if (response.data.code === 200) {
      selectedProfile.value = response.data.data
      showDetailModal.value = true
    }
  } catch (error) {
    console.error('获取档案详情失败:', error)
    alert('获取档案详情失败')
  }
}

const editProfile = (profile) => {
  selectedProfile.value = profile
  Object.assign(profileForm, {
    profileOwnerName: profile.profileOwnerName || '',
    gender: profile.gender || '',
    birthDate: profile.birthDate || '',
    idCard: profile.idCard || '',
    medicalHistory: profile.medicalHistory || ''
  })
  showEditModal.value = true
}

const deleteProfile = async (profile) => {
  if (!confirm(`确定要删除 ${profile.profileOwnerName} 的健康档案吗？`)) {
    return
  }
  
  try {
    const response = await healthApi.deleteHealthProfile(profile.id)
    if (response.data.code === 200) {
      alert('删除成功')
      loadProfiles()
    } else {
      alert(response.data.message || '删除失败')
    }
  } catch (error) {
    console.error('删除档案失败:', error)
    alert('删除失败，请稍后重试')
  }
}

const submitProfile = async () => {
  try {
    submitting.value = true
    
    let response
    if (isEditMode.value) {
      response = await healthApi.updateHealthProfile(selectedProfile.value.id, profileForm)
    } else {
      response = await healthApi.createHealthProfile(profileForm)
    }
    
    if (response.data.code === 200) {
      alert(isEditMode.value ? '更新成功' : '创建成功')
      closeModals()
      loadProfiles()
    } else {
      alert(response.data.message || '操作失败')
    }
  } catch (error) {
    console.error('提交档案失败:', error)
    alert('操作失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

const closeModals = () => {
  showCreateModal.value = false
  showEditModal.value = false
  showDetailModal.value = false
  selectedProfile.value = null
  
  // 重置表单
  Object.assign(profileForm, {
    profileOwnerName: '',
    gender: '',
    birthDate: '',
    idCard: '',
    medicalHistory: ''
  })
}

// 工具函数
const getInitials = (name) => {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

const getGenderText = (gender) => {
  const genderMap = {
    'MALE': '男',
    'FEMALE': '女',
    'OTHER': '其他'
  }
  return genderMap[gender] || ''
}

const maskIdCard = (idCard) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (datetime) => {
  if (!datetime) return ''
  return new Date(datetime).toLocaleString('zh-CN')
}

// 组件挂载
onMounted(() => {
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }
  
  loadProfiles()
})
</script>

<style scoped>
.health-profiles {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  font-size: 16px;
  margin: 0 0 24px 0;
}

.header-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.create-btn, .refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s;
}

.create-btn {
  background: #4A90E2;
  color: white;
}

.create-btn:hover {
  background: #357abd;
}

.refresh-btn {
  background: #6c757d;
  color: white;
}

.refresh-btn:hover {
  background: #5a6268;
}

.refresh-btn:disabled {
  background: #adb5bd;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 18px;
}

.profiles-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading {
  text-align: center;
  padding: 60px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 24px;
}

.empty-state p {
  color: #6c757d;
  margin-bottom: 30px;
}

.profiles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  padding: 20px;
}

.profile-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s;
  background: white;
}

.profile-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.profile-header {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.profile-avatar, .detail-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  flex-shrink: 0;
}

.profile-avatar img, .detail-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #4A90E2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
}

.profile-info h3 {
  margin: 0 0 4px 0;
  color: #2c3e50;
  font-size: 18px;
}

.profile-details {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.profile-body {
  padding: 20px;
}

.info-item, .detail-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item label, .detail-item label {
  font-weight: 500;
  color: #495057;
  min-width: 80px;
  margin-right: 10px;
}

.info-item span, .detail-item span {
  color: #6c757d;
  flex: 1;
}

.profile-actions {
  display: flex;
  gap: 8px;
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.action-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.action-btn.view {
  background: #4A90E2;
  color: white;
}

.action-btn.view:hover {
  background: #357abd;
}

.action-btn.edit {
  background: #28a745;
  color: white;
}

.action-btn.edit:hover {
  background: #218838;
}

.action-btn.delete {
  background: #dc3545;
  color: white;
}

.action-btn.delete:hover {
  background: #c82333;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn:not(:disabled):hover {
  border-color: #4A90E2;
  background: #f8f9fa;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

.page-number {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.page-number:hover {
  border-color: #4A90E2;
  background: #f8f9fa;
}

.page-number.active {
  background: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-height: 90vh;
  overflow: auto;
}

.profile-modal {
  width: 90%;
  max-width: 500px;
}

.detail-modal {
  width: 90%;
  max-width: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
  background: #f8f9fa;
  border-radius: 50%;
}

.modal-body {
  padding: 20px;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4A90E2;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn, .submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.submit-btn {
  background: #4A90E2;
  color: white;
}

.submit-btn:hover {
  background: #357abd;
}

.submit-btn:disabled {
  background: #adb5bd;
  cursor: not-allowed;
}

.profile-detail {
  max-width: 100%;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.detail-info h2 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 24px;
}

.detail-info p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

@media (max-width: 768px) {
  .profiles-grid {
    grid-template-columns: 1fr;
    padding: 15px;
  }
  
  .header-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .profile-actions {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
