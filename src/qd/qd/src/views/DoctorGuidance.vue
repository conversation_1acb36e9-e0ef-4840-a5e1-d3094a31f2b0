<template>
  <div class="doctor-guidance">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">📚</div>
        <div class="header-text">
          <h1>健康指导</h1>
          <p>发布专业的健康指导内容，帮助患者了解健康知识</p>
        </div>
      </div>
      <div class="header-actions">
        <button class="btn btn-primary" @click="showCreateModal">
          <i class="icon">✍️</i>
          发布新指导
        </button>
      </div>
    </div>

    <!-- 指导列表 -->
    <div class="guidance-container">
      <div class="guidance-list" v-if="guidanceList.length > 0">
        <div 
          v-for="guidance in guidanceList" 
          :key="guidance.id" 
          class="guidance-card">
          
          <!-- 卡片头部 -->
          <div class="card-header">
            <h3 class="guidance-title">{{ guidance.title }}</h3>
            <div class="card-actions">
              <button class="btn btn-sm btn-outline" @click="viewGuidance(guidance)">
                查看
              </button>
              <button class="btn btn-sm btn-secondary" @click="editGuidance(guidance)">
                编辑
              </button>
              <button class="btn btn-sm btn-danger" @click="deleteGuidance(guidance)">
                删除
              </button>
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content">
            <p class="guidance-summary">{{ guidance.summary }}</p>
            <div class="guidance-meta">
              <span class="meta-item">
                <i class="icon">📅</i>
                发布时间: {{ formatDateTime(guidance.publishedAt) }}
              </span>
              <span class="meta-item">
                <i class="icon">👁️</i>
                浏览量: {{ guidance.viewCount || 0 }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">📝</div>
        <h3>暂无健康指导</h3>
        <p>您还没有发布任何健康指导，点击上方按钮开始发布</p>
        <button class="btn btn-primary" @click="showCreateModal">
          发布第一篇指导
        </button>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination">
        <button 
          @click="prevPage" 
          :disabled="currentPage <= 1"
          class="page-btn">
          上一页
        </button>
        <span class="page-info">第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
        <button 
          @click="nextPage" 
          :disabled="currentPage >= totalPages"
          class="page-btn">
          下一页
        </button>
      </div>
    </div>

    <!-- 创建/编辑弹窗 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content guidance-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ isEditing ? '编辑健康指导' : '发布健康指导' }}</h3>
          <button class="close-btn" @click="closeModal">&times;</button>
        </div>
        
        <div class="modal-body">
          <form @submit.prevent="submitGuidance">
            <!-- 标题 -->
            <div class="form-group">
              <label for="title">标题 *</label>
              <input 
                type="text" 
                id="title"
                v-model="guidanceForm.title" 
                placeholder="请输入健康指导标题"
                required>
            </div>

            <!-- 内容 -->
            <div class="form-group">
              <label for="content">内容 *</label>
              <textarea 
                id="content"
                v-model="guidanceForm.body" 
                placeholder="请输入健康指导内容，可以包括症状描述、预防措施、治疗建议等"
                rows="12"
                required></textarea>
            </div>
          </form>
        </div>
        
        <div class="modal-footer">
          <button 
            class="btn btn-primary"
            @click="submitGuidance"
            :disabled="submitting">
            {{ submitting ? '发布中...' : (isEditing ? '更新' : '发布') }}
          </button>
          <button class="btn btn-secondary" @click="closeModal">取消</button>
        </div>
      </div>
    </div>

    <!-- 查看详情弹窗 -->
    <div v-if="showViewModal" class="modal-overlay" @click="closeViewModal">
      <div class="modal-content view-modal" @click.stop>
        <div class="modal-header">
          <h3>健康指导详情</h3>
          <button class="close-btn" @click="closeViewModal">&times;</button>
        </div>
        
        <div class="modal-body" v-if="selectedGuidance">
          <div class="view-header">
            <h2>{{ selectedGuidance.title }}</h2>
            <div class="view-meta">
              <span class="publish-time">发布时间: {{ formatDateTime(selectedGuidance.publishedAt) }}</span>
              <span class="view-count">浏览量: {{ selectedGuidance.viewCount || 0 }}</span>
            </div>
          </div>
          
          <div class="view-content">
            <div class="content-text" v-html="formatContent(selectedGuidance.body)"></div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeViewModal">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as contentApi from '@/api/content'

// 响应式数据
const guidanceList = ref([])
const currentPage = ref(1)
const totalPages = ref(1)
const totalElements = ref(0)
const loading = ref(false)

// 弹窗状态
const showModal = ref(false)
const showViewModal = ref(false)
const isEditing = ref(false)
const submitting = ref(false)
const selectedGuidance = ref(null)
const editingId = ref(null)

// 表单数据
const guidanceForm = ref({
  title: '',
  body: ''
})

// 加载健康指导列表
const loadGuidanceList = async () => {
  try {
    loading.value = true
    const response = await contentApi.getDoctorGuidanceList({
      page: currentPage.value,
      size: 10
    })

    if (response.data.code === 200) {
      guidanceList.value = response.data.data.content || []
      totalPages.value = response.data.data.totalPages || 1
      totalElements.value = response.data.data.totalElements || 0

      // 为每个指导添加摘要
      guidanceList.value.forEach(guidance => {
        if (guidance.body && !guidance.summary) {
          guidance.summary = guidance.body.length > 100
            ? guidance.body.substring(0, 100) + '...'
            : guidance.body
        }
      })
    } else {
      console.error('加载健康指导失败:', response.data.message)
      alert('加载健康指导失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('加载健康指导失败:', error)
    alert('加载健康指导失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 分页操作
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    loadGuidanceList()
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    loadGuidanceList()
  }
}

// 显示创建弹窗
const showCreateModal = () => {
  isEditing.value = false
  editingId.value = null
  guidanceForm.value = {
    title: '',
    body: ''
  }
  showModal.value = true
}

// 编辑健康指导
const editGuidance = async (guidance) => {
  try {
    const response = await contentApi.getDoctorGuidanceDetail(guidance.id)
    if (response.data.code === 200) {
      const detail = response.data.data
      isEditing.value = true
      editingId.value = guidance.id
      guidanceForm.value = {
        title: detail.title,
        body: detail.body
      }
      showModal.value = true
    } else {
      alert('获取指导详情失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取指导详情失败:', error)
    alert('获取指导详情失败，请稍后重试')
  }
}

// 查看健康指导
const viewGuidance = async (guidance) => {
  try {
    const response = await contentApi.getDoctorGuidanceDetail(guidance.id)
    if (response.data.code === 200) {
      selectedGuidance.value = response.data.data
      showViewModal.value = true
    } else {
      alert('获取指导详情失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取指导详情失败:', error)
    alert('获取指导详情失败，请稍后重试')
  }
}

// 删除健康指导
const deleteGuidance = async (guidance) => {
  if (!confirm(`确定要删除"${guidance.title}"吗？`)) return

  try {
    const response = await contentApi.deleteDoctorGuidance(guidance.id)
    if (response.data.code === 200) {
      alert('删除成功')
      loadGuidanceList()
    } else {
      alert('删除失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('删除失败:', error)
    alert('删除失败，请稍后重试')
  }
}

// 提交健康指导
const submitGuidance = async () => {
  if (!guidanceForm.value.title || !guidanceForm.value.body) {
    alert('请填写完整信息')
    return
  }

  try {
    submitting.value = true

    const submitData = {
      title: guidanceForm.value.title,
      body: guidanceForm.value.body
    }

    let response
    if (isEditing.value) {
      response = await contentApi.updateDoctorGuidance(editingId.value, submitData)
    } else {
      response = await contentApi.createDoctorGuidance(submitData)
    }

    if (response.data.code === 200) {
      alert(isEditing.value ? '更新成功' : '发布成功')
      closeModal()
      loadGuidanceList()
    } else {
      alert((isEditing.value ? '更新' : '发布') + '失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('提交失败:', error)
    alert('提交失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 关闭弹窗
const closeModal = () => {
  showModal.value = false
  isEditing.value = false
  editingId.value = null
  guidanceForm.value = {
    title: '',
    body: ''
  }
}

const closeViewModal = () => {
  showViewModal.value = false
  selectedGuidance.value = null
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '未知'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 格式化内容
const formatContent = (content) => {
  if (!content) return ''
  return content.replace(/\n/g, '<br>')
}

// 页面初始化
onMounted(() => {
  loadGuidanceList()
})
</script>

<style scoped>
.doctor-guidance {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.header-text p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions .btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.guidance-container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.guidance-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.guidance-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
}

.guidance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.guidance-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 20px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 14px;
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 1px solid #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border: 1px solid #6c757d;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: 1px solid #dc3545;
}

.btn-danger:hover {
  background: #c82333;
}

.card-content {
  color: #555;
}

.guidance-summary {
  margin: 0 0 16px 0;
  font-size: 14px;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.guidance-meta {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #888;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-item .icon {
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 20px;
}

.empty-state p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}

.page-btn {
  padding: 10px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.page-btn:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.guidance-modal {
  max-width: 800px;
}

.view-modal {
  max-width: 700px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #666;
}

.modal-body {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e1e5e9;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.view-header h2 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 22px;
  font-weight: 600;
}

.view-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #666;
}

.view-content {
  color: #555;
  line-height: 1.6;
}

.content-text {
  font-size: 15px;
  line-height: 1.8;
}

@media (max-width: 768px) {
  .doctor-guidance {
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .guidance-card {
    padding: 20px;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .card-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .modal-content {
    margin: 10px;
    max-width: none;
  }

  .guidance-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
