<template>
  <div class="community-activities">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">🌟</div>
        <div class="header-text">
          <h1>社区活动</h1>
          <p>参与社区健康活动，共建美好生活</p>
        </div>
      </div>
    </div>

    <!-- 活动列表 -->
    <div class="activities-container">
      <div class="activities-grid" v-if="activities.length > 0">
        <div 
          v-for="activity in activities" 
          :key="activity.id" 
          class="activity-card"
          @click="viewActivityDetail(activity)">
          
          <!-- 活动状态标签 -->
          <div class="activity-status">
            <span v-if="activity.isRegistered" class="status-registered">已报名</span>
            <span v-else-if="isActivityExpired(activity)" class="status-expired">已结束</span>
            <span v-else class="status-available">可报名</span>
          </div>

          <!-- 活动内容 -->
          <div class="activity-content">
            <h3 class="activity-title">{{ activity.title }}</h3>
            <p class="activity-summary">{{ activity.summary }}</p>
            
            <!-- 活动信息 -->
            <div class="activity-info">
              <div class="info-item" v-if="activity.activityTime">
                <i class="icon">📅</i>
                <span>{{ formatDateTime(activity.activityTime) }}</span>
              </div>
              <div class="info-item" v-if="activity.activityLocation">
                <i class="icon">📍</i>
                <span>{{ activity.activityLocation }}</span>
              </div>
              <div class="info-item">
                <i class="icon">👥</i>
                <span>{{ activity.registrationCount || 0 }} 人已报名</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="activity-actions">
              <button 
                v-if="!activity.isRegistered && !isActivityExpired(activity)"
                class="btn btn-primary"
                @click.stop="registerForActivity(activity)">
                立即报名
              </button>
              <button 
                v-else-if="activity.isRegistered"
                class="btn btn-success"
                disabled>
                已报名
              </button>
              <button 
                v-else
                class="btn btn-disabled"
                disabled>
                活动已结束
              </button>
              <button class="btn btn-outline" @click.stop="viewActivityDetail(activity)">
                查看详情
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">🎪</div>
        <h3>暂无活动</h3>
        <p>目前还没有社区活动，请稍后再来查看</p>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination">
        <button 
          @click="prevPage" 
          :disabled="currentPage <= 1"
          class="page-btn">
          上一页
        </button>
        <span class="page-info">第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
        <button 
          @click="nextPage" 
          :disabled="currentPage >= totalPages"
          class="page-btn">
          下一页
        </button>
      </div>
    </div>

    <!-- 活动详情弹窗 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content activity-detail-modal" @click.stop>
        <div class="modal-header">
          <h3>活动详情</h3>
          <button class="close-btn" @click="closeDetailModal">&times;</button>
        </div>
        
        <div class="modal-body" v-if="selectedActivity">
          <div class="detail-header">
            <h2>{{ selectedActivity.title }}</h2>
            <div class="detail-meta">
              <span class="activity-type">社区活动</span>
              <span class="publish-time">发布时间: {{ formatDateTime(selectedActivity.publishedAt) }}</span>
            </div>
          </div>
          
          <!-- 活动信息 -->
          <div class="activity-details">
            <div class="detail-item" v-if="selectedActivity.activityTime">
              <strong>活动时间:</strong> {{ formatDateTime(selectedActivity.activityTime) }}
            </div>
            <div class="detail-item" v-if="selectedActivity.activityLocation">
              <strong>活动地点:</strong> {{ selectedActivity.activityLocation }}
            </div>
            <div class="detail-item">
              <strong>报名人数:</strong> {{ selectedActivity.registrationCount || 0 }} 人
            </div>
            <div class="detail-item" v-if="selectedActivity.authorName">
              <strong>发布者:</strong> {{ selectedActivity.authorName }}
            </div>
          </div>
          
          <!-- 活动内容 -->
          <div class="activity-body">
            <h4>活动详情</h4>
            <div class="content-text" v-html="formatContent(selectedActivity.body)"></div>
          </div>
        </div>
        
        <div class="modal-footer" v-if="selectedActivity">
          <button 
            v-if="!selectedActivity.isRegistered && !isActivityExpired(selectedActivity)"
            class="btn btn-primary"
            @click="registerForActivity(selectedActivity)">
            立即报名
          </button>
          <button 
            v-else-if="selectedActivity.isRegistered"
            class="btn btn-success"
            disabled>
            已报名
          </button>
          <button class="btn btn-secondary" @click="closeDetailModal">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import * as contentApi from '@/api/content'

// 响应式数据
const activities = ref([])
const currentPage = ref(1)
const totalPages = ref(1)
const totalElements = ref(0)
const loading = ref(false)
const showDetailModal = ref(false)
const selectedActivity = ref(null)

// 加载活动列表
const loadActivities = async () => {
  try {
    loading.value = true
    const response = await contentApi.getActivities({
      page: currentPage.value,
      size: 10
    })

    if (response.data.code === 200) {
      activities.value = response.data.data.content || []
      totalPages.value = response.data.data.totalPages || 1
      totalElements.value = response.data.data.totalElements || 0
    } else {
      console.error('加载活动列表失败:', response.data.message)
      alert('加载活动列表失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('加载活动列表失败:', error)
    alert('加载活动列表失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 分页操作
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    loadActivities()
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    loadActivities()
  }
}

// 查看活动详情
const viewActivityDetail = async (activity) => {
  try {
    const response = await contentApi.getContentDetail(activity.id)
    if (response.data.code === 200) {
      selectedActivity.value = response.data.data
      showDetailModal.value = true
    } else {
      alert('获取活动详情失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取活动详情失败:', error)
    alert('获取活动详情失败，请稍后重试')
  }
}

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  selectedActivity.value = null
}

// 报名活动
const registerForActivity = async (activity) => {
  if (!confirm(`确定要报名参加"${activity.title}"吗？`)) return

  try {
    const response = await contentApi.registerActivity(activity.id)
    if (response.data.code === 200) {
      alert('报名成功！')
      // 更新活动状态
      activity.isRegistered = true
      activity.registrationCount = (activity.registrationCount || 0) + 1
      
      // 如果详情弹窗打开，也更新详情数据
      if (selectedActivity.value && selectedActivity.value.id === activity.id) {
        selectedActivity.value.isRegistered = true
        selectedActivity.value.registrationCount = activity.registrationCount
      }
    } else {
      alert('报名失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('报名失败:', error)
    alert('报名失败，请稍后重试')
  }
}

// 检查活动是否已过期
const isActivityExpired = (activity) => {
  if (!activity.activityTime) return false
  return new Date(activity.activityTime) < new Date()
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '未知'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 格式化内容
const formatContent = (content) => {
  if (!content) return ''
  return content.replace(/\n/g, '<br>')
}

// 页面初始化
onMounted(() => {
  loadActivities()
})
</script>

<style scoped>
.community-activities {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.page-header {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.header-text p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.activities-container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
}

.activity-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.activity-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.activity-status {
  position: absolute;
  top: 16px;
  right: 16px;
}

.activity-status span {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-registered {
  background: #d4edda;
  color: #155724;
}

.status-expired {
  background: #f8d7da;
  color: #721c24;
}

.status-available {
  background: #d1ecf1;
  color: #0c5460;
}

.activity-title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  padding-right: 80px;
}

.activity-summary {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.activity-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #555;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .icon {
  font-size: 16px;
}

.activity-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 1px solid #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

.btn-disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 20px;
}

.empty-state p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}

.page-btn {
  padding: 10px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.page-btn:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #666;
}

.modal-body {
  padding: 24px;
}

.detail-header h2 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 22px;
  font-weight: 600;
}

.detail-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #666;
}

.activity-type {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.activity-details {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.detail-item {
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item strong {
  color: #333;
  font-weight: 600;
}

.activity-body h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.content-text {
  color: #555;
  line-height: 1.6;
  font-size: 14px;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e1e5e9;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .community-activities {
    padding: 15px;
  }

  .activities-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .activity-card {
    padding: 20px;
  }

  .activity-actions {
    flex-direction: column;
  }

  .modal-content {
    margin: 10px;
    max-width: none;
  }
}
</style>
