<template>
  <div class="system-settings">
    <h2>系统设置</h2>

    <!-- 系统参数设置 -->
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <span>系统参数设置</span>
        </div>
      </template>
      <el-form :model="systemParams" label-width="200px">
        <el-form-item v-for="param in systemParams" :key="param.id" :label="param.name">
          <el-input v-model="param.value" @change="updateSystemParam(param)"></el-input>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 字典管理 -->
    <el-card class="settings-card">
      <template #header>
        <div class="card-header">
          <span>字典管理</span>
          <el-button type="primary" @click="showDictionaryDialog('add')">新增字典项</el-button>
        </div>
      </template>
      <el-tabs v-model="activeTab">
        <el-tab-pane v-for="type in dictionaryTypes" :key="type" :label="type" :name="type">
          <el-table :data="dictionaries[type]" style="width: 100%">
            <el-table-column prop="name" label="名称"></el-table-column>
            <el-table-column prop="code" label="编码"></el-table-column>
            <el-table-column prop="sort" label="排序"></el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button size="small" @click="showDictionaryDialog('edit', scope.row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteDictionary(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 字典编辑对话框 -->
    <el-dialog
      :title="dictionaryDialog.type === 'add' ? '新增字典项' : '编辑字典项'"
      v-model="dictionaryDialog.visible"
      width="500px"
    >
      <el-form :model="dictionaryDialog.form" label-width="100px">
        <el-form-item label="类型" v-if="dictionaryDialog.type === 'add'">
          <el-select v-model="dictionaryDialog.form.type">
            <el-option
              v-for="type in dictionaryTypes"
              :key="type"
              :label="type"
              :value="type"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称">
          <el-input v-model="dictionaryDialog.form.name"></el-input>
        </el-form-item>
        <el-form-item label="编码">
          <el-input v-model="dictionaryDialog.form.code"></el-input>
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="dictionaryDialog.form.sort"></el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dictionaryDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveDictionary">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getSystemParams,
  updateSystemParam,
  getDictionariesByType,
  createDictionary,
  updateDictionary,
  deleteDictionary
} from '@/api/admin'

export default {
  name: 'SystemSettings',
  setup() {
    const systemParams = ref([])
    const dictionaries = ref({})
    const dictionaryTypes = ref(['DOCTOR_TITLE', 'DEPARTMENT'])
    const activeTab = ref('DOCTOR_TITLE')

    const dictionaryDialog = reactive({
      visible: false,
      type: 'add',
      form: {
        id: null,
        type: '',
        name: '',
        code: '',
        sort: 0
      }
    })

    // 获取系统参数
    const fetchSystemParams = async () => {
      try {
        const response = await getSystemParams()
        systemParams.value = response.data
      } catch (error) {
        ElMessage.error('获取系统参数失败')
      }
    }

    // 更新系统参数
    const handleUpdateSystemParam = async (param) => {
      try {
        await updateSystemParam(param)
        ElMessage.success('更新成功')
      } catch (error) {
        ElMessage.error('更新失败')
      }
    }

    // 获取字典数据
    const fetchDictionaries = async (type) => {
      try {
        const response = await getDictionariesByType(type)
        dictionaries.value[type] = response.data
      } catch (error) {
        ElMessage.error('获取字典数据失败')
      }
    }

    // 显示字典对话框
    const showDictionaryDialog = (type, row) => {
      dictionaryDialog.type = type
      if (type === 'add') {
        dictionaryDialog.form = {
          id: null,
          type: activeTab.value,
          name: '',
          code: '',
          sort: 0
        }
      } else {
        dictionaryDialog.form = { ...row }
      }
      dictionaryDialog.visible = true
    }

    // 保存字典
    const saveDictionary = async () => {
      try {
        if (dictionaryDialog.type === 'add') {
          await createDictionary(dictionaryDialog.form)
        } else {
          await updateDictionary(dictionaryDialog.form)
        }
        ElMessage.success('保存成功')
        dictionaryDialog.visible = false
        fetchDictionaries(dictionaryDialog.form.type)
      } catch (error) {
        ElMessage.error('保存失败')
      }
    }

    // 删除字典
    const handleDeleteDictionary = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除该字典项吗？', '提示', {
          type: 'warning'
        })
        await deleteDictionary(row.id)
        ElMessage.success('删除成功')
        fetchDictionaries(row.type)
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }

    onMounted(() => {
      fetchSystemParams()
      dictionaryTypes.value.forEach(type => {
        fetchDictionaries(type)
      })
    })

    return {
      systemParams,
      dictionaries,
      dictionaryTypes,
      activeTab,
      dictionaryDialog,
      updateSystemParam: handleUpdateSystemParam,
      showDictionaryDialog,
      saveDictionary,
      deleteDictionary: handleDeleteDictionary
    }
  }
}
</script>

<style scoped>
.system-settings {
  padding: 20px;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style>