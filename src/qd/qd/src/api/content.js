import api from './index'

// ==================== 用户端内容相关接口 ====================

/**
 * 获取活动列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @returns {Promise} 活动列表响应
 */
export const getActivities = (params) => {
  return api.get('/user/contents/activities', { params })
}

/**
 * 获取健康资讯列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @returns {Promise} 健康资讯列表响应
 */
export const getHealthNews = (params) => {
  return api.get('/user/contents/news', { params })
}

/**
 * 获取医生指导列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @returns {Promise} 医生指导列表响应
 */
export const getDoctorGuidance = (params) => {
  return api.get('/user/contents/guidance', { params })
}

/**
 * 获取内容详情
 * @param {number} contentId - 内容ID
 * @returns {Promise} 内容详情响应
 */
export const getContentDetail = (contentId) => {
  return api.get(`/user/contents/${contentId}`)
}

/**
 * 报名参加活动
 * @param {number} activityId - 活动ID
 * @returns {Promise} 报名响应
 */
export const registerActivity = (activityId) => {
  return api.post(`/user/contents/activities/${activityId}/register`)
}

// ==================== 医生端内容相关接口 ====================

/**
 * 获取医生发布的健康指导列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @returns {Promise} 健康指导列表响应
 */
export const getDoctorGuidanceList = (params) => {
  return api.get('/doctor/contents/guidance', { params })
}

/**
 * 创建健康指导
 * @param {Object} data - 健康指导数据
 * @param {string} data.title - 标题
 * @param {string} data.body - 内容
 * @returns {Promise} 创建响应
 */
export const createDoctorGuidance = (data) => {
  return api.post('/doctor/contents/guidance', data)
}

/**
 * 更新健康指导
 * @param {number} contentId - 内容ID
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新响应
 */
export const updateDoctorGuidance = (contentId, data) => {
  return api.put(`/doctor/contents/guidance/${contentId}`, data)
}

/**
 * 删除健康指导
 * @param {number} contentId - 内容ID
 * @returns {Promise} 删除响应
 */
export const deleteDoctorGuidance = (contentId) => {
  return api.delete(`/doctor/contents/guidance/${contentId}`)
}

/**
 * 获取健康指导详情
 * @param {number} contentId - 内容ID
 * @returns {Promise} 详情响应
 */
export const getDoctorGuidanceDetail = (contentId) => {
  return api.get(`/doctor/contents/guidance/${contentId}`)
}

export default {
  getActivities,
  getHealthNews,
  getDoctorGuidance,
  getContentDetail,
  registerActivity,
  getDoctorGuidanceList,
  createDoctorGuidance,
  updateDoctorGuidance,
  deleteDoctorGuidance,
  getDoctorGuidanceDetail
}
