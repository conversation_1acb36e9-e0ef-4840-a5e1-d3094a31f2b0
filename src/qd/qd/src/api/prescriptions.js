import api from './index'

/**
 * 为预约创建诊断处方
 * @param {Object} prescriptionData - 处方数据
 * @param {number} prescriptionData.profileId - 健康档案ID
 * @param {number} prescriptionData.appointmentId - 预约ID
 * @param {string} prescriptionData.diagnosis - 诊断信息
 * @param {Array} prescriptionData.medications - 药品列表
 * @returns {Promise} 创建处方响应
 */
export const createPrescription = (prescriptionData) => {
  console.log('=== 创建诊断处方API调用 ===')
  console.log('处方数据:', prescriptionData)
  
  return api.post(`/doctor/appointments/${prescriptionData.appointmentId}/prescribe`, prescriptionData)
}

/**
 * 获取预约的诊断处方
 * @param {number} appointmentId - 预约ID
 * @returns {Promise} 处方详情响应
 */
export const getPrescriptionByAppointment = (appointmentId) => {
  console.log('=== 获取预约处方API调用 ===')
  console.log('预约ID:', appointmentId)
  
  return api.get(`/doctor/appointments/${appointmentId}/prescription`)
}

/**
 * 获取医生的诊断处方列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @returns {Promise} 处方列表响应
 */
export const getDoctorPrescriptions = (params = {}) => {
  console.log('=== 获取医生处方列表API调用 ===')
  console.log('查询参数:', params)
  
  return api.get('/doctor/prescriptions/my', { params })
}

/**
 * 获取患者的处方列表
 * @param {number} profileId - 健康档案ID
 * @param {Object} params - 查询参数
 * @returns {Promise} 患者处方列表响应
 */
export const getPatientPrescriptions = (profileId, params = {}) => {
  console.log('=== 获取患者处方列表API调用 ===')
  console.log('健康档案ID:', profileId)
  console.log('查询参数:', params)
  
  return api.get(`/profiles/${profileId}/prescriptions`, { params })
}

/**
 * 获取处方详情
 * @param {number} prescriptionId - 处方ID
 * @returns {Promise} 处方详情响应
 */
export const getPrescriptionDetail = (prescriptionId) => {
  console.log('=== 获取处方详情API调用 ===')
  console.log('处方ID:', prescriptionId)
  
  return api.get(`/prescriptions/${prescriptionId}`)
}
