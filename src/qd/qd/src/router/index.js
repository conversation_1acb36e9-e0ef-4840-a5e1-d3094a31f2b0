import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/Dashboard.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/doctor',
      name: 'doctor-dashboard',
      component: () => import('../views/DoctorDashboard.vue'),
      meta: { requiresAuth: true, requiresDoctor: true }
    },
    {
      path: '/doctor/profile',
      name: 'doctor-profile',
      component: () => import('../views/DoctorProfile.vue'),
      meta: { requiresAuth: true, requiresDoctor: true }
    },
    {
      path: '/doctor/schedule',
      name: 'doctor-schedule',
      component: () => import('../views/DoctorSchedule.vue'),
      meta: { requiresAuth: true, requiresDoctor: true }
    },
    {
      path: '/doctor/appointments',
      name: 'doctor-appointments',
      component: () => import('../views/DoctorAppointments.vue'),
      meta: { requiresAuth: true, requiresDoctor: true }
    },
    {
      path: '/doctor/statistics',
      name: 'doctor-statistics',
      component: () => import('../views/DoctorStatisticsAnalysis.vue'),
      meta: { requiresAuth: true, requiresDoctor: true }
    },
    {
      path: '/doctor/statistics/test',
      name: 'statistics-test',
      component: () => import('../views/StatisticsTestPage.vue'),
      meta: { requiresAuth: true, requiresDoctor: true }
    },
    {
      path: '/doctor/statistics/simple',
      name: 'statistics-simple',
      component: () => import('../views/DoctorStatisticsSimple.vue'),
      meta: { requiresAuth: true, requiresDoctor: true }
    },
    {
      path: '/doctor/consultations',
      name: 'doctor-consultations',
      component: () => import('../views/DoctorConsultation.vue'),
      meta: { requiresAuth: true, requiresDoctor: true }
    },
    {
      path: '/doctor/prescriptions',
      name: 'doctor-prescriptions',
      component: () => import('../views/DoctorPrescription.vue'),
      meta: { requiresAuth: true, requiresDoctor: true }
    },
    {
      path: '/consultations',
      name: 'resident-consultations',
      component: () => import('../views/ResidentConsultation.vue'),
      meta: { requiresAuth: true, requiresResident: true }
    },
    {
      path: '/prescriptions',
      name: 'resident-prescriptions',
      component: () => import('../views/ResidentPrescription.vue'),
      meta: { requiresAuth: true, requiresResident: true }
    },
    {
      path: '/consultation-test',
      name: 'consultation-test',
      component: () => import('../views/ConsultationTestPage.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/consultation-nav',
      name: 'consultation-navigation',
      component: () => import('../views/ConsultationNavigation.vue')
    },
    {
      path: '/debug-user',
      name: 'debug-user-info',
      component: () => import('../views/DebugUserInfo.vue')
    },
    {
      path: '/admin',
      name: 'admin-dashboard',
      component: () => import('../views/AdminDashboard.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/profile',
      name: 'health-profile',
      component: () => import('../views/HealthProfile.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/health-profiles',
      name: 'health-profiles',
      component: () => import('../views/HealthProfiles.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile/create',
      name: 'create-health-profile',
      component: () => import('../views/CreateHealthProfile.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/records',
      name: 'health-records',
      component: () => import('../views/HealthRecords.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/reminders',
      name: 'health-reminders',
      component: () => import('../views/HealthReminders.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/appointments',
      name: 'patient-appointments',
      component: () => import('../views/PatientAppointments.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/booking',
      name: 'appointment-booking',
      component: () => import('../views/AppointmentBooking.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/activities',
      name: 'community-activities',
      component: () => import('../views/CommunityActivities.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/doctor/guidance',
      name: 'doctor-guidance',
      component: () => import('../views/DoctorGuidance.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/Register.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/test',
      name: 'test',
      component: () => import('../views/TestPage.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/api-test-fix',
      name: 'api-test-fix',
      component: () => import('../views/ApiTestFixPage.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/api-test-validation',
      name: 'api-test-validation',
      component: () => import('../views/ApiTestReportValidation.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/api-test',
      name: 'api-test',
      component: () => import('../views/ApiTestPage.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/appointment-test',
      name: 'appointment-test',
      component: () => import('../views/AppointmentTestPage.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/debug-api',
      name: 'debug-api',
      component: () => import('../views/DebugApiPage.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/system-status',
      name: 'system-status',
      component: () => import('../views/SystemStatusPage.vue'),
      meta: { requiresAuth: true }
    },
  ],
})

// 全局前置路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  // 检查路由是否只允许游客访问（如登录、注册页）
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)

  // 如果用户已登录
  if (userStore.isLoggedIn) {
    // 如果访问的是游客页面（登录、注册），根据用户角色重定向
    if (requiresGuest) {

      // 使用正确的计算属性进行角色判断
      if (userStore.isAdmin) {
        console.log('重定向到管理员页面')
        next('/admin')
      } else if (userStore.isDoctor) {
        console.log('重定向到医生页面')
        next('/doctor')
      } else if (userStore.isResident) {
        console.log('重定向到居民页面')
        next('/')
      } else {
        console.log('未知角色，重定向到首页')
        next('/')
      }
      return
    }

    // 验证token有效性
    const isTokenValid = await userStore.checkTokenValidity()
    if (!isTokenValid) {
      // token无效，重定向到登录页
      next('/login')
      return
    }

    // 检查医生权限
    const requiresDoctor = to.matched.some(record => record.meta.requiresDoctor)
    if (requiresDoctor && !userStore.isDoctor) {
      console.log('医生权限检查失败:', userStore.isDoctor)
      next('/')
      return
    }

    // 检查居民权限
    const requiresResident = to.matched.some(record => record.meta.requiresResident)
    if (requiresResident && !userStore.isResident) {
      console.log('居民权限检查失败:', userStore.isResident)
      next('/')
      return
    }

    // 检查管理员权限
    const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)
    if (requiresAdmin && !userStore.isAdmin) {
      console.log('管理员权限检查失败:', userStore.isAdmin)
      next('/')
      return
    }
  } else {
    // 用户未登录，如果访问需要认证的页面，重定向到登录页
    if (requiresAuth) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // 允许访问
  next()
})

export default router
